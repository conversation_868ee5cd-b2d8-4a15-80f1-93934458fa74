package com.kibi.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kibi.entity.CreateChat;
import com.kibi.entity.User;
import com.kibi.service.CreateChatService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/createChat")
public class AdminCreateChatController {

    @Autowired
    private CreateChatService createChatService;

    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取初始聊天列表 - 分页查询
     */
    @GetMapping("/list")
    public R<Map<String, Object>> getCreateChatList(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 构建查询条件
            QueryWrapper<CreateChat> queryWrapper = new QueryWrapper<>();
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like("title", keyword)
                           .or().like("content", keyword);
            }
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<CreateChat> pageParam = new Page<>(page, pageSize);
            IPage<CreateChat> createChatPage = createChatService.page(pageParam, queryWrapper);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("items", createChatPage.getRecords());
            result.put("total", createChatPage.getTotal());
            result.put("page", createChatPage.getCurrent());
            result.put("pageSize", createChatPage.getSize());
            result.put("totalPages", createChatPage.getPages());

            return R.success(result);
        } catch (Exception e) {
            System.err.println("获取初始聊天列表异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取初始聊天列表失败");
        }
    }

    /**
     * 创建初始聊天
     */
    @PostMapping
    public R<CreateChat> createChat(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody CreateChat createChat) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 设置默认值
            if (createChat.getStatus() == null) {
                createChat.setStatus(1);
            }

            // 保存初始聊天
            boolean success = createChatService.save(createChat);
            if (success) {
                return R.success(createChat);
            } else {
                return R.error("创建初始聊天失败");
            }
        } catch (Exception e) {
            System.err.println("创建初始聊天异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("创建初始聊天失败");
        }
    }

    /**
     * 更新初始聊天
     */
    @PutMapping("/{id}")
    public R<CreateChat> updateCreateChat(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id,
            @RequestBody CreateChat createChat) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查初始聊天是否存在
            CreateChat existingCreateChat = createChatService.getById(id);
            if (existingCreateChat == null) {
                return R.error("初始聊天不存在");
            }

            // 更新初始聊天信息
            createChat.setId(id);
            boolean success = createChatService.updateById(createChat);
            if (success) {
                return R.success(createChatService.getById(id));
            } else {
                return R.error("更新初始聊天失败");
            }
        } catch (Exception e) {
            System.err.println("更新初始聊天异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新初始聊天失败");
        }
    }

    /**
     * 删除初始聊天
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteCreateChat(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查初始聊天是否存在
            CreateChat createChat = createChatService.getById(id);
            if (createChat == null) {
                return R.error("初始聊天不存在");
            }

            // 删除初始聊天
            boolean success = createChatService.removeById(id);
            return success ? R.success(true) : R.error("删除初始聊天失败");
        } catch (Exception e) {
            System.err.println("删除初始聊天异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("删除初始聊天失败");
        }
    }

    /**
     * 获取单个初始聊天信息
     */
    @GetMapping("/{id}")
    public R<CreateChat> getCreateChat(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            CreateChat createChat = createChatService.getById(id);
            if (createChat == null) {
                return R.error("初始聊天不存在");
            }

            return R.success(createChat);
        } catch (Exception e) {
            System.err.println("获取初始聊天信息异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取初始聊天信息失败");
        }
    }

    /**
     * 批量删除初始聊天
     */
    @DeleteMapping("/batch")
    public R<Boolean> batchDeleteCreateChats(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<Long> ids) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            if (ids.isEmpty()) {
                return R.error("没有可删除的初始聊天");
            }

            // 批量删除
            boolean success = createChatService.removeByIds(ids);
            return success ? R.success(true) : R.error("批量删除失败");
        } catch (Exception e) {
            System.err.println("批量删除初始聊天异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("批量删除失败");
        }
    }

    /**
     * 验证管理员Token
     */
    private boolean validateAdminToken(String authHeader) {
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return false;
            }

            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return false;
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null || user.getStatus() != 1) {
                return false;
            }

            // 只有管理员账户才有权限
            return userId == 88888L;
        } catch (Exception e) {
            return false;
        }
    }
}
