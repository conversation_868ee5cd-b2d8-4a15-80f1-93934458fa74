<template>
  <view class="create-chat-management">
    <!-- 初始聊天管理头部 -->
    <view class="management-header">
      <view class="header-title">初始聊天管理</view>
      <view class="header-actions">
        <button
          class="action-btn primary"
          @click="handleAddCreateChat"
        >
          <text class="btn-icon">➕</text>
          添加初始聊天
        </button>
      </view>
    </view>

    <!-- 数据表格 -->
    <DataTable
      :data="createChatCrud.data.value"
      :columns="createChatColumns"
      :loading="createChatCrud.loading.value"
      :pagination="createChatCrud.pagination"
      :searchable="true"
      :sortable="true"
      :actions="tableActions"
      :selectable="true"
      :batch-actions="batchActions"
      title="初始聊天列表"
      search-placeholder="搜索标题、内容"
      @search="createChatCrud.search"
      @sort="createChatCrud.sort"
      @page-change="createChatCrud.changePage"
      @page-size-change="createChatCrud.changePageSize"
      @batch-action="handleBatchAction"
    />

    <!-- 初始聊天表单弹窗 -->
    <view v-if="showCreateChatForm" class="modal-overlay" @click="closeCreateChatForm">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ formMode === 'create' ? '添加初始聊天' : formMode === 'edit' ? '编辑初始聊天' : '查看初始聊天' }}</view>
          <view class="modal-close" @click="closeCreateChatForm">✕</view>
        </view>

        <view class="modal-body">
          <DataForm
            :fields="createChatFormFields"
            :data="formData"
            :mode="formMode"
            :loading="createChatCrud.submitting.value"
            @submit="handleCreateChatSubmit"
            @cancel="closeCreateChatForm"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCrud } from '../../composables/useCrud'
import { createChatApi } from '../../api/user'
import DataTable from '../common/DataTable.vue'
import DataForm from '../common/DataForm.vue'
import type { TableColumn, TableAction, FormField } from '../../types/admin'

// 响应式数据
const showCreateChatForm = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const formData = ref<any>({})

// 使用CRUD composable
const createChatCrud = useCrud<any>(createChatApi, {
  initialPagination: { pageSize: 10 },
  autoLoad: true,
  cacheKey: 'createChats'
})

// 表格列配置
const createChatColumns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: '80px',
    sortable: true
  },
  {
    key: 'title',
    title: '标题',
    sortable: true
  },
  {
    key: 'content',
    title: '内容',
    render: (value: string) => value && value.length > 50 ? value.substring(0, 50) + '...' : value
  },
  {
    key: 'type',
    title: '类型',
    render: (value: string) => {
      const typeMap: Record<string, string> = {
        'greeting': '问候语',
        'question': '问题',
        'topic': '话题',
        'other': '其他'
      }
      return typeMap[value] || value
    }
  },
  {
    key: 'status',
    title: '状态',
    render: (value: number) => value === 1 ? '启用' : '禁用'
  },
  {
    key: 'createTime',
    title: '创建时间'
  }
]

// 表格操作
const tableActions = computed<TableAction[]>(() => [
  {
    type: 'view',
    label: '查看',
    handler: handleViewCreateChat
  },
  {
    type: 'edit',
    label: '编辑',
    handler: handleEditCreateChat
  },
  {
    type: 'delete',
    label: '删除',
    handler: handleDeleteCreateChat
  }
])

// 批量操作
const batchActions = computed<TableAction[]>(() => [
  {
    type: 'delete',
    label: '批量删除',
    handler: handleBatchDelete
  }
])

// 初始聊天表单字段
const createChatFormFields = computed<FormField[]>(() => [
  {
    key: 'title',
    label: '标题',
    type: 'text',
    required: true,
    placeholder: '请输入标题',
    rules: [
      { type: 'required', message: '标题不能为空' },
      { type: 'max', value: 100, message: '标题最多100个字符' }
    ]
  },
  {
    key: 'content',
    label: '内容',
    type: 'textarea',
    required: true,
    placeholder: '请输入聊天内容',
    rules: [
      { type: 'required', message: '内容不能为空' },
      { type: 'max', value: 1000, message: '内容最多1000个字符' }
    ]
  },
  {
    key: 'type',
    label: '类型',
    type: 'select',
    required: true,
    options: [
      { label: '问候语', value: 'greeting' },
      { label: '问题', value: 'question' },
      { label: '话题', value: 'topic' },
      { label: '其他', value: 'other' }
    ]
  },
  {
    key: 'tags',
    label: '标签',
    type: 'text',
    placeholder: '请输入标签，多个标签用逗号分隔'
  },
  {
    key: 'status',
    label: '状态',
    type: 'radio',
    required: true,
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
])

// 方法
function handleAddCreateChat() {
  formMode.value = 'create'
  formData.value = {
    title: '',
    content: '',
    type: 'greeting',
    tags: '',
    status: 1
  }
  showCreateChatForm.value = true
}

function handleViewCreateChat(createChat: any) {
  formMode.value = 'view'
  formData.value = { ...createChat }
  showCreateChatForm.value = true
}

function handleEditCreateChat(createChat: any) {
  formMode.value = 'edit'
  formData.value = { ...createChat }
  showCreateChatForm.value = true
}

async function handleDeleteCreateChat(createChat: any) {
  try {
    const success = await createChatCrud.remove(createChat.id)
    if (success) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('Delete createChat error:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  }
}

function closeCreateChatForm() {
  showCreateChatForm.value = false
  formData.value = {}
}

async function handleCreateChatSubmit(data: any) {
  try {
    let result
    if (formMode.value === 'create') {
      result = await createChatCrud.create(data)
    } else {
      result = await createChatCrud.update(formData.value.id!, data)
    }

    if (result) {
      uni.showToast({
        title: formMode.value === 'create' ? '创建成功' : '更新成功',
        icon: 'success'
      })
      closeCreateChatForm()
    }
  } catch (error) {
    console.error('Submit createChat error:', error)
    uni.showToast({
      title: formMode.value === 'create' ? '创建失败' : '更新失败',
      icon: 'error'
    })
  }
}

async function handleBatchAction(action: TableAction, selectedItems: any[]) {
  if (action.type === 'delete') {
    await handleBatchDelete(selectedItems)
  }
}

async function handleBatchDelete(createChats: any[]) {
  try {
    const ids = createChats.map(createChat => createChat.id)
    await createChatCrud.batchRemove(ids)
    uni.showToast({
      title: '批量删除成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('Batch delete error:', error)
    uni.showToast({
      title: '批量删除失败',
      icon: 'error'
    })
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.create-chat-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        .btn-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          cursor: pointer;
          font-size: 18px;
          color: #999;

          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }
}
</style>
