<template>
  <view class="partner-management">
    <!-- 伴侣管理头部 -->
    <view class="management-header">
      <view class="header-title">伴侣管理</view>
      <view class="header-actions">
        <button
          class="action-btn primary"
          @click="handleAddPartner"
        >
          <text class="btn-icon">➕</text>
          添加伴侣
        </button>
      </view>
    </view>

    <!-- 数据表格 -->
    <DataTable
      :data="partnerCrud.data.value"
      :columns="partnerColumns"
      :loading="partnerCrud.loading.value"
      :pagination="partnerCrud.pagination"
      :searchable="true"
      :sortable="true"
      :actions="tableActions"
      :selectable="true"
      :batch-actions="batchActions"
      title="伴侣列表"
      search-placeholder="搜索伴侣名称、描述"
      @search="partnerCrud.search"
      @sort="partnerCrud.sort"
      @page-change="partnerCrud.changePage"
      @page-size-change="partnerCrud.changePageSize"
      @batch-action="handleBatchAction"
    />

    <!-- 伴侣表单弹窗 -->
    <view v-if="showPartnerForm" class="modal-overlay" @click="closePartnerForm">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ formMode === 'create' ? '添加伴侣' : formMode === 'edit' ? '编辑伴侣' : '查看伴侣' }}</view>
          <view class="modal-close" @click="closePartnerForm">✕</view>
        </view>

        <view class="modal-body">
          <DataForm
            :fields="partnerFormFields"
            :data="formData"
            :mode="formMode"
            :loading="partnerCrud.submitting.value"
            @submit="handlePartnerSubmit"
            @cancel="closePartnerForm"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useCrud } from '../../composables/useCrud'
import { partnerApi } from '../../api/user'
import DataTable from '../common/DataTable.vue'
import DataForm from '../common/DataForm.vue'
import type { TableColumn, TableAction, FormField } from '../../types/admin'

// 响应式数据
const showPartnerForm = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const formData = ref<any>({})

// 使用CRUD composable
const partnerCrud = useCrud<any>(partnerApi, {
  initialPagination: { pageSize: 10 },
  autoLoad: true,
  cacheKey: 'partners'
})

// 表格列配置
const partnerColumns: TableColumn[] = [
  {
    key: 'id',
    title: 'ID',
    width: '80px',
    sortable: true
  },
  {
    key: 'name',
    title: '伴侣名称',
    sortable: true
  },
  {
    key: 'description',
    title: '描述'
  },
  {
    key: 'personality',
    title: '性格特征'
  },
  {
    key: 'status',
    title: '状态',
    render: (value: number) => value === 1 ? '启用' : '禁用'
  },
  {
    key: 'createTime',
    title: '创建时间'
  }
]

// 表格操作
const tableActions = computed<TableAction[]>(() => [
  {
    type: 'view',
    label: '查看',
    handler: handleViewPartner
  },
  {
    type: 'edit',
    label: '编辑',
    handler: handleEditPartner
  },
  {
    type: 'delete',
    label: '删除',
    handler: handleDeletePartner
  }
])

// 批量操作
const batchActions = computed<TableAction[]>(() => [
  {
    type: 'delete',
    label: '批量删除',
    handler: handleBatchDelete
  }
])

// 伴侣表单字段
const partnerFormFields = computed<FormField[]>(() => [
  {
    key: 'name',
    label: '伴侣名称',
    type: 'text',
    required: true,
    placeholder: '请输入伴侣名称',
    rules: [
      { type: 'required', message: '伴侣名称不能为空' },
      { type: 'max', value: 50, message: '伴侣名称最多50个字符' }
    ]
  },
  {
    key: 'description',
    label: '描述',
    type: 'textarea',
    placeholder: '请输入伴侣描述',
    rules: [
      { type: 'max', value: 200, message: '描述最多200个字符' }
    ]
  },
  {
    key: 'personality',
    label: '性格特征',
    type: 'textarea',
    placeholder: '请输入性格特征',
    rules: [
      { type: 'max', value: 500, message: '性格特征最多500个字符' }
    ]
  },
  {
    key: 'avatar',
    label: '头像',
    type: 'text',
    placeholder: '请输入头像URL'
  },
  {
    key: 'status',
    label: '状态',
    type: 'radio',
    required: true,
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
])

// 方法
function handleAddPartner() {
  formMode.value = 'create'
  formData.value = {
    name: '',
    description: '',
    personality: '',
    avatar: '',
    status: 1
  }
  showPartnerForm.value = true
}

function handleViewPartner(partner: any) {
  formMode.value = 'view'
  formData.value = { ...partner }
  showPartnerForm.value = true
}

function handleEditPartner(partner: any) {
  formMode.value = 'edit'
  formData.value = { ...partner }
  showPartnerForm.value = true
}

async function handleDeletePartner(partner: any) {
  try {
    const success = await partnerCrud.remove(partner.id)
    if (success) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('Delete partner error:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  }
}

function closePartnerForm() {
  showPartnerForm.value = false
  formData.value = {}
}

async function handlePartnerSubmit(data: any) {
  try {
    let result
    if (formMode.value === 'create') {
      result = await partnerCrud.create(data)
    } else {
      result = await partnerCrud.update(formData.value.id!, data)
    }

    if (result) {
      uni.showToast({
        title: formMode.value === 'create' ? '创建成功' : '更新成功',
        icon: 'success'
      })
      closePartnerForm()
    }
  } catch (error) {
    console.error('Submit partner error:', error)
    uni.showToast({
      title: formMode.value === 'create' ? '创建失败' : '更新失败',
      icon: 'error'
    })
  }
}

async function handleBatchAction(action: TableAction, selectedItems: any[]) {
  if (action.type === 'delete') {
    await handleBatchDelete(selectedItems)
  }
}

async function handleBatchDelete(partners: any[]) {
  try {
    const ids = partners.map(partner => partner.id)
    await partnerCrud.batchRemove(ids)
    uni.showToast({
      title: '批量删除成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('Batch delete error:', error)
    uni.showToast({
      title: '批量删除失败',
      icon: 'error'
    })
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.partner-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.primary {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          &:hover {
            background-color: #40a9ff;
          }
        }

        .btn-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #e8e8e8;

        .modal-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .modal-close {
          cursor: pointer;
          font-size: 18px;
          color: #999;

          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }
}
</style>
