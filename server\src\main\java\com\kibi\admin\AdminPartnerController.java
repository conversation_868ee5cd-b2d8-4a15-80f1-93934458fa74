package com.kibi.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kibi.entity.Partner;
import com.kibi.entity.User;
import com.kibi.service.PartnerService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/partner")
public class AdminPartnerController {

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取伴侣列表 - 分页查询
     */
    @GetMapping("/list")
    public R<Map<String, Object>> getPartnerList(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 构建查询条件
            QueryWrapper<Partner> queryWrapper = new QueryWrapper<>();
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like("name", keyword)
                           .or().like("description", keyword);
            }
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<Partner> pageParam = new Page<>(page, pageSize);
            IPage<Partner> partnerPage = partnerService.page(pageParam, queryWrapper);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("items", partnerPage.getRecords());
            result.put("total", partnerPage.getTotal());
            result.put("page", partnerPage.getCurrent());
            result.put("pageSize", partnerPage.getSize());
            result.put("totalPages", partnerPage.getPages());

            return R.success(result);
        } catch (Exception e) {
            System.err.println("获取伴侣列表异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取伴侣列表失败");
        }
    }

    /**
     * 创建伴侣
     */
    @PostMapping
    public R<Partner> createPartner(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody Partner partner) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 设置默认值
            if (partner.getStatus() == null) {
                partner.setStatus(1);
            }

            // 保存伴侣
            boolean success = partnerService.save(partner);
            if (success) {
                return R.success(partner);
            } else {
                return R.error("创建伴侣失败");
            }
        } catch (Exception e) {
            System.err.println("创建伴侣异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("创建伴侣失败");
        }
    }

    /**
     * 更新伴侣
     */
    @PutMapping("/{id}")
    public R<Partner> updatePartner(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id,
            @RequestBody Partner partner) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查伴侣是否存在
            Partner existingPartner = partnerService.getById(id);
            if (existingPartner == null) {
                return R.error("伴侣不存在");
            }

            // 更新伴侣信息
            partner.setId(id);
            boolean success = partnerService.updateById(partner);
            if (success) {
                return R.success(partnerService.getById(id));
            } else {
                return R.error("更新伴侣失败");
            }
        } catch (Exception e) {
            System.err.println("更新伴侣异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新伴侣失败");
        }
    }

    /**
     * 删除伴侣
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deletePartner(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查伴侣是否存在
            Partner partner = partnerService.getById(id);
            if (partner == null) {
                return R.error("伴侣不存在");
            }

            // 删除伴侣
            boolean success = partnerService.removeById(id);
            return success ? R.success(true) : R.error("删除伴侣失败");
        } catch (Exception e) {
            System.err.println("删除伴侣异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("删除伴侣失败");
        }
    }

    /**
     * 获取单个伴侣信息
     */
    @GetMapping("/{id}")
    public R<Partner> getPartner(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            Partner partner = partnerService.getById(id);
            if (partner == null) {
                return R.error("伴侣不存在");
            }

            return R.success(partner);
        } catch (Exception e) {
            System.err.println("获取伴侣信息异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取伴侣信息失败");
        }
    }

    /**
     * 批量删除伴侣
     */
    @DeleteMapping("/batch")
    public R<Boolean> batchDeletePartners(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<Long> ids) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            if (ids.isEmpty()) {
                return R.error("没有可删除的伴侣");
            }

            // 批量删除
            boolean success = partnerService.removeByIds(ids);
            return success ? R.success(true) : R.error("批量删除失败");
        } catch (Exception e) {
            System.err.println("批量删除伴侣异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("批量删除失败");
        }
    }

    /**
     * 验证管理员Token
     */
    private boolean validateAdminToken(String authHeader) {
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return false;
            }

            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return false;
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null || user.getStatus() != 1) {
                return false;
            }

            // 只有管理员账户才有权限
            return userId == 88888L;
        } catch (Exception e) {
            return false;
        }
    }
}