package com.kibi.admin;

import com.kibi.utils.R;
import org.springframework.web.bind.annotation.*;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.*;

@RestController
@RequestMapping("/admin/monitor")
public class AdminMonitorController {

    /**
     * 获取服务器状态
     */
    @GetMapping("/server-status")
    public R<Map<String, Object>> getServerStatus() {
        try {
            RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
            long uptime = runtimeMXBean.getUptime();
            
            // 计算运行时间
            long days = uptime / (24 * 60 * 60 * 1000);
            long hours = (uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
            long minutes = (uptime % (60 * 60 * 1000)) / (60 * 1000);
            
            String uptimeStr = String.format("%d天 %d小时 %d分钟", days, hours, minutes);
            
            Map<String, Object> serverStatus = new HashMap<>();
            serverStatus.put("status", "healthy");
            serverStatus.put("text", "运行正常");
            serverStatus.put("uptime", uptimeStr);
            
            return R.success(serverStatus);
        } catch (Exception e) {
            Map<String, Object> serverStatus = new HashMap<>();
            serverStatus.put("status", "error");
            serverStatus.put("text", "状态异常");
            serverStatus.put("uptime", "未知");
            
            return R.success(serverStatus);
        }
    }

    /**
     * 获取系统指标
     */
    @GetMapping("/system-metrics")
    public R<Map<String, Object>> getSystemMetrics() {
        try {
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            
            // CPU使用率（简化计算）
            double cpuUsage = osBean.getProcessCpuLoad() * 100;
            if (cpuUsage < 0) {
                cpuUsage = Math.random() * 50 + 10; // 模拟数据
            }
            
            // 内存使用情况
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            double memoryUsage = (double) usedMemory / maxMemory * 100;
            
            // 磁盘使用情况（模拟数据）
            double diskUsage = Math.random() * 60 + 30;
            
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("cpuUsage", Math.round(cpuUsage));
            metrics.put("memoryUsage", Math.round(memoryUsage));
            metrics.put("usedMemory", Math.round(usedMemory / (1024.0 * 1024.0 * 1024.0) * 10) / 10.0);
            metrics.put("totalMemory", Math.round(maxMemory / (1024.0 * 1024.0 * 1024.0) * 10) / 10.0);
            metrics.put("diskUsage", Math.round(diskUsage));
            metrics.put("usedDisk", 245);
            metrics.put("totalDisk", 500);
            
            return R.success(metrics);
        } catch (Exception e) {
            // 返回模拟数据
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("cpuUsage", 35);
            metrics.put("memoryUsage", 65);
            metrics.put("usedMemory", 6.4);
            metrics.put("totalMemory", 16.0);
            metrics.put("diskUsage", 48);
            metrics.put("usedDisk", 245);
            metrics.put("totalDisk", 500);
            
            return R.success(metrics);
        }
    }

    /**
     * 获取数据库指标
     */
    @GetMapping("/database-metrics")
    public R<Map<String, Object>> getDatabaseMetrics() {
        // 这里应该连接数据库获取真实指标，现在返回模拟数据
        Map<String, Object> dbMetrics = new HashMap<>();
        dbMetrics.put("connections", (int)(Math.random() * 40) + 10);
        dbMetrics.put("queriesPerSecond", (int)(Math.random() * 80) + 40);
        dbMetrics.put("avgResponseTime", (int)(Math.random() * 40) + 10);
        dbMetrics.put("slowQueries", (int)(Math.random() * 3));
        dbMetrics.put("status", "healthy");
        
        return R.success(dbMetrics);
    }

    /**
     * 获取健康检查
     */
    @GetMapping("/health-checks")
    public R<List<Map<String, Object>>> getHealthChecks() {
        List<Map<String, Object>> healthChecks = new ArrayList<>();
        
        // API服务检查
        Map<String, Object> apiCheck = new HashMap<>();
        apiCheck.put("name", "API服务");
        apiCheck.put("description", "主要API服务运行状态");
        apiCheck.put("status", "healthy");
        apiCheck.put("lastCheck", new Date().toString());
        healthChecks.add(apiCheck);
        
        // 数据库连接检查
        Map<String, Object> dbCheck = new HashMap<>();
        dbCheck.put("name", "数据库连接");
        dbCheck.put("description", "数据库连接池状态");
        dbCheck.put("status", "healthy");
        dbCheck.put("lastCheck", new Date().toString());
        healthChecks.add(dbCheck);
        
        // 缓存服务检查
        Map<String, Object> cacheCheck = new HashMap<>();
        cacheCheck.put("name", "缓存服务");
        cacheCheck.put("description", "Redis缓存服务状态");
        cacheCheck.put("status", Math.random() > 0.7 ? "warning" : "healthy");
        cacheCheck.put("lastCheck", new Date().toString());
        healthChecks.add(cacheCheck);
        
        // 文件存储检查
        Map<String, Object> storageCheck = new HashMap<>();
        storageCheck.put("name", "文件存储");
        storageCheck.put("description", "文件存储服务状态");
        storageCheck.put("status", "healthy");
        storageCheck.put("lastCheck", new Date().toString());
        healthChecks.add(storageCheck);
        
        return R.success(healthChecks);
    }

    /**
     * 获取系统告警
     */
    @GetMapping("/alerts")
    public R<List<Map<String, Object>>> getAlerts() {
        List<Map<String, Object>> alerts = new ArrayList<>();
        
        // 随机生成一些告警
        if (Math.random() > 0.5) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("id", "1");
            alert.put("level", "warning");
            alert.put("title", "内存使用率较高");
            alert.put("message", "系统内存使用率已达到75%，建议检查内存泄漏问题");
            alert.put("timestamp", new Date().toString());
            
            List<Map<String, String>> actions = new ArrayList<>();
            Map<String, String> action1 = new HashMap<>();
            action1.put("name", "check_memory");
            action1.put("label", "检查内存");
            actions.add(action1);
            
            Map<String, String> action2 = new HashMap<>();
            action2.put("name", "restart_service");
            action2.put("label", "重启服务");
            actions.add(action2);
            
            alert.put("actions", actions);
            alerts.add(alert);
        }
        
        if (Math.random() > 0.7) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("id", "2");
            alert.put("level", "error");
            alert.put("title", "数据库连接异常");
            alert.put("message", "检测到数据库连接超时，请检查网络连接");
            alert.put("timestamp", new Date().toString());
            alerts.add(alert);
        }
        
        return R.success(alerts);
    }

    /**
     * 获取系统日志
     */
    @GetMapping("/logs")
    public R<List<Map<String, Object>>> getLogs(@RequestParam(required = false) List<String> levels,
                                                 @RequestParam(defaultValue = "100") int limit) {
        List<Map<String, Object>> logs = new ArrayList<>();
        
        // 生成模拟日志数据
        String[] logLevels = {"INFO", "WARN", "ERROR", "DEBUG"};
        String[] messages = {
            "用户登录成功: <EMAIL>",
            "数据库连接池使用率较高: 85%",
            "API请求失败: /api/users/profile - 500 Internal Server Error",
            "系统启动完成",
            "缓存清理完成",
            "定时任务执行成功",
            "文件上传失败: 文件大小超限",
            "用户权限验证失败"
        };
        
        for (int i = 0; i < Math.min(limit, 50); i++) {
            Map<String, Object> log = new HashMap<>();
            log.put("id", String.valueOf(i + 1));
            log.put("timestamp", new Date(System.currentTimeMillis() - i * 60000).toString());
            log.put("level", logLevels[(int)(Math.random() * logLevels.length)]);
            log.put("message", messages[(int)(Math.random() * messages.length)]);
            
            // 如果指定了日志级别过滤
            if (levels != null && !levels.isEmpty()) {
                if (levels.contains(log.get("level"))) {
                    logs.add(log);
                }
            } else {
                logs.add(log);
            }
        }
        
        return R.success(logs);
    }
}
