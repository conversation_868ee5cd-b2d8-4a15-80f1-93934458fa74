package com.kibi.admin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kibi.entity.Role;
import com.kibi.entity.User;
import com.kibi.service.RoleService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/role")
public class AdminRoleController {

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取角色列表 - 分页查询
     */
    @GetMapping("/list")
    public R<Map<String, Object>> getRoleList(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 构建查询条件
            QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like("name", keyword)
                           .or().like("code", keyword);
            }
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<Role> pageParam = new Page<>(page, pageSize);
            IPage<Role> rolePage = roleService.page(pageParam, queryWrapper);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("items", rolePage.getRecords());
            result.put("total", rolePage.getTotal());
            result.put("page", rolePage.getCurrent());
            result.put("pageSize", rolePage.getSize());
            result.put("totalPages", rolePage.getPages());

            return R.success(result);
        } catch (Exception e) {
            System.err.println("获取角色列表异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取角色列表失败");
        }
    }

    /**
     * 创建角色
     */
    @PostMapping
    public R<Role> createRole(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody Role role) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查角色代码是否已存在
            QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", role.getCode());
            if (roleService.count(queryWrapper) > 0) {
                return R.error("角色代码已存在");
            }

            // 设置默认值
            if (role.getStatus() == null) {
                role.setStatus(1);
            }

            // 保存角色
            boolean success = roleService.save(role);
            if (success) {
                return R.success(role);
            } else {
                return R.error("创建角色失败");
            }
        } catch (Exception e) {
            System.err.println("创建角色异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("创建角色失败");
        }
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    public R<Role> updateRole(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id,
            @RequestBody Role role) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查角色是否存在
            Role existingRole = roleService.getById(id);
            if (existingRole == null) {
                return R.error("角色不存在");
            }

            // 如果修改了角色代码，检查是否重复
            if (!existingRole.getCode().equals(role.getCode())) {
                QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("code", role.getCode())
                           .ne("id", id);
                if (roleService.count(queryWrapper) > 0) {
                    return R.error("角色代码已存在");
                }
            }

            // 更新角色信息
            role.setId(id);
            boolean success = roleService.updateById(role);
            if (success) {
                return R.success(roleService.getById(id));
            } else {
                return R.error("更新角色失败");
            }
        } catch (Exception e) {
            System.err.println("更新角色异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新角色失败");
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteRole(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            // 检查角色是否存在
            Role role = roleService.getById(id);
            if (role == null) {
                return R.error("角色不存在");
            }

            // 检查是否有用户使用该角色
            // 这里需要根据实际的用户角色关联表来查询
            // 暂时跳过这个检查

            // 删除角色
            boolean success = roleService.removeById(id);
            return success ? R.success(true) : R.error("删除角色失败");
        } catch (Exception e) {
            System.err.println("删除角色异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("删除角色失败");
        }
    }

    /**
     * 获取单个角色信息
     */
    @GetMapping("/{id}")
    public R<Role> getRole(
            @RequestHeader("Authorization") String authHeader,
            @PathVariable Long id) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            Role role = roleService.getById(id);
            if (role == null) {
                return R.error("角色不存在");
            }

            return R.success(role);
        } catch (Exception e) {
            System.err.println("获取角色信息异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取角色信息失败");
        }
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    public R<Boolean> batchDeleteRoles(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<Long> ids) {

        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            if (ids.isEmpty()) {
                return R.error("没有可删除的角色");
            }

            // 批量删除
            boolean success = roleService.removeByIds(ids);
            return success ? R.success(true) : R.error("批量删除失败");
        } catch (Exception e) {
            System.err.println("批量删除角色异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("批量删除失败");
        }
    }

    /**
     * 获取所有角色（用于下拉选择）
     */
    @GetMapping("/all")
    public R<List<Role>> getAllRoles(@RequestHeader("Authorization") String authHeader) {
        try {
            // 验证管理员权限
            if (!validateAdminToken(authHeader)) {
                return R.error("无权限访问");
            }

            QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1)
                       .orderBy(true, true, "sort", "name");

            List<Role> roles = roleService.list(queryWrapper);
            return R.success(roles);
        } catch (Exception e) {
            System.err.println("获取所有角色异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("获取角色列表失败");
        }
    }

    /**
     * 验证管理员Token
     */
    private boolean validateAdminToken(String authHeader) {
        try {
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return false;
            }

            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return false;
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null || user.getStatus() != 1) {
                return false;
            }

            // 只有管理员账户才有权限
            return userId == 88888L;
        } catch (Exception e) {
            return false;
        }
    }
}